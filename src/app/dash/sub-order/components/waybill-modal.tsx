import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CopyButton } from "@/components/ui/button/copy-button";
import {
  FileTextIcon,
  PackageIcon,
  AlertCircleIcon,
  LoaderIcon
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { subOrderApi } from "@/api/sub-order/sub-order-api";
import type { WaybillResponse } from "@/api/sub-order/sub-order-model";
import { Separator } from "@/components/ui/separator";

interface WaybillModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subOrderId: number;
  customerOrderNo: string;
}

export function WaybillModal({ open, onOpenChange, subOrderId, customerOrderNo }: WaybillModalProps) {
  const { t } = useTranslation();
  const [waybillInfo, setWaybillInfo] = useState<WaybillResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取面单信息
  const fetchWaybillInfo = async () => {
    if (!open || !subOrderId) return;

    setLoading(true);
    setError(null);

    try {
      const data = await subOrderApi.getWaybillInfo(subOrderId);
      setWaybillInfo(data);
      if (!data) {
        setError(t('waybill.noWaybillInfo'));
      }
    } catch (err) {
      setError(t('waybill.fetchError'));
      console.error('Failed to fetch waybill info:', err);
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开时获取数据
  useEffect(() => {
    if (open) {
      fetchWaybillInfo();
    } else {
      // 关闭时清理状态
      setWaybillInfo(null);
      setError(null);
    }
  }, [open, subOrderId]);

  // 渲染加载状态
  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileTextIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div>{t('waybill.title')}</div>
                <p className="text-sm font-normal text-muted-foreground">
                  {t('waybill.orderNo')}: {customerOrderNo}
                </p>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center gap-3">
              <LoaderIcon className="h-8 w-8 animate-spin text-blue-600" />
              <p className="text-sm text-muted-foreground">{t('waybill.loading')}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileTextIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div>{t('waybill.title')}</div>
                <p className="text-sm font-normal text-muted-foreground">
                  {t('waybill.orderNo')}: {customerOrderNo}
                </p>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center gap-3 text-center">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertCircleIcon className="h-8 w-8 text-red-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{t('waybill.error')}</p>
                <p className="text-sm text-muted-foreground mt-1">{error}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchWaybillInfo}
                className="mt-2"
              >
                {t('waybill.retry')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileTextIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <div>{t('waybill.title')}</div>
              <p className="text-sm font-normal text-muted-foreground">
                {t('waybill.orderNo')}: {customerOrderNo}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6">
            {/* 面单基本信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <PackageIcon className="h-4 w-4" />
                {t('waybill.basicInfo')}
              </h3>

              <div className="grid grid-cols-1 gap-3">
                {/* 运单号 */}
                {waybillInfo?.waybillNo && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{t('waybill.waybillNo')}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono">
                        {waybillInfo.waybillNo}
                      </Badge>
                      <CopyButton text={waybillInfo.waybillNo} size="xs" />
                    </div>
                  </div>
                )}

                {/* 跟踪号 */}
                {waybillInfo?.trackingNumber && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{t('waybill.trackingNumber')}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono">
                        {waybillInfo.trackingNumber}
                      </Badge>
                      <CopyButton text={waybillInfo.trackingNumber} size="xs" />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* PDF操作区域 */}
            <Separator className="my-4" />
            {waybillInfo?.pdfUrl && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <FileTextIcon className="h-4 w-4" />
                  {t('waybill.pdfDocument')}
                </h3>

                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = waybillInfo.pdfUrl!;
                      link.download = `waybill-${waybillInfo.waybillNo || customerOrderNo}.pdf`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    className="flex items-center gap-2"
                  >
                    {t('waybill.downloadPdf')}
                  </Button>
                </div>
              </div>
            )}

            {/* 无数据提示 */}
            {!waybillInfo?.waybillNo && !waybillInfo?.trackingNumber && !waybillInfo?.pdfUrl && (
              <div className="text-center py-8">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-3 bg-gray-100 rounded-full">
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{t('waybill.noData')}</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {t('waybill.noDataDescription')}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
