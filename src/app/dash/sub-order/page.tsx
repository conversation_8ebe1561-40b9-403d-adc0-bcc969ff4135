import { useState, useCallback, useEffect } from "react";
import { DataTable } from "@/components/table/base-table";
import { subOrderColumns } from "./sub-order-column";
import { subOrderApi } from "@/api/sub-order/sub-order-api";
import { SubOrderSearch } from "./components/sub-order-search";
import { TrackingModal } from "./components/tracking-modal";
import { WaybillModal } from "./components/waybill-modal";
import type { FetchParams } from "@/components/table/types";
import { Separator } from "@/components/ui/separator";
import { useTranslation } from "react-i18next";

export default function SubOrderPage() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [trackingModalOpen, setTrackingModalOpen] = useState(false);
  const [waybillModalOpen, setWaybillModalOpen] = useState(false);
  const [selectedSubOrder, setSelectedSubOrder] = useState<{ id: number; customerOrderNo: string } | null>(null);

  // 数据获取函数
  const fetchSubOrders = useCallback(async (params: FetchParams) => {
    setLoading(true);
    try {
      return await subOrderApi.searchSubOrders(params);
    } finally {
      setLoading(false);
    }
  }, []);

  // 监听打开轨迹模态框的事件
  useEffect(() => {
    const handleOpenTrackingModal = (event: CustomEvent) => {
      const { subOrderId, customerOrderNo } = event.detail;
      setSelectedSubOrder({ id: subOrderId, customerOrderNo });
      setTrackingModalOpen(true);
    };

    const handleOpenWaybillModal = (event: CustomEvent) => {
      const { subOrderId, customerOrderNo } = event.detail;
      setSelectedSubOrder({ id: subOrderId, customerOrderNo });
      setWaybillModalOpen(true);
    };

    window.addEventListener('openTrackingModal', handleOpenTrackingModal as EventListener);
    window.addEventListener('openWaybillModal', handleOpenWaybillModal as EventListener);

    return () => {
      window.removeEventListener('openTrackingModal', handleOpenTrackingModal as EventListener);
      window.removeEventListener('openWaybillModal', handleOpenWaybillModal as EventListener);
    };
  }, []);

  return (
    <div className="max-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
      <div className="flex flex-col gap-4 h-full">
        <header className="px-6 py-2 space-y-1">
          <h1 className="text-[18px] font-medium">{t('subOrder.title')}</h1>
          <h2 className="text-[13px] text-zinc-500">
            {t('subOrder.description')}
          </h2>
        </header>

        <Separator />

        {/* 搜索区域 */}
        <div className="px-2">
          <SubOrderSearch loading={loading} />
        </div>

        {/* 数据表格 */}
        <div className="flex-1 min-w-0">
          <DataTable
            columns={subOrderColumns}
            onFetch={fetchSubOrders}
            defaultPageSize={20}
            isFixedHeader={true}
            containerHeight="calc(100vh - 390px)"
            className="w-full"
          />
        </div>
      </div>

      {/* 轨迹查看模态框 */}
      {selectedSubOrder && (
        <TrackingModal
          open={trackingModalOpen}
          onOpenChange={setTrackingModalOpen}
          subOrderId={selectedSubOrder.id}
          customerOrderNo={selectedSubOrder.customerOrderNo}
        />
      )}

      {/* 面单查看模态框 */}
      {selectedSubOrder && (
        <WaybillModal
          open={waybillModalOpen}
          onOpenChange={setWaybillModalOpen}
          subOrderId={selectedSubOrder.id}
          customerOrderNo={selectedSubOrder.customerOrderNo}
        />
      )}
    </div>
  );
}
