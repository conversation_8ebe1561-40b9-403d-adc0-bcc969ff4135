import { apiClient } from "@/lib/apiClient";
import type { FetchParams, FetchResponse } from "@/components/table/types";
import type {
  SubOrderPageRequest,
  SubOrderPageResponseOptimized,
  SubOrder,
  SubOrderResponse,
  WaybillResponse
} from "./sub-order-model";
import { convertFilterStatusToQueryStatus } from "@/utils/sub-order-status";

export const subOrderApi = {
  /**
   * 分页查询子订单（优化版本）
   * @param params 查询参数
   * @returns 分页结果，只包含前端需要的字段
   */
  searchSubOrders: async (params: FetchParams): Promise<FetchResponse<SubOrderResponse>> => {
    // 处理状态筛选参数
    let statusParam = params.searchParams?.status;
    if (statusParam) {
      // 将筛选器状态转换为查询状态
      const filterStatuses = statusParam.split(',').filter((s: string) => s.trim());
      const queryStatuses = convertFilterStatusToQueryStatus(filterStatuses);
      statusParam = queryStatuses.join(',');
    }

    // 构建请求参数
    const request: SubOrderPageRequest = {
      page: params.pagination.pageIndex,
      size: params.pagination.pageSize,
      sortBy: params.sort?.[0],
      sortDirection: params.direction?.[0]?.toLowerCase() as 'asc' | 'desc',
      ...params.searchParams,
      ...params.advancedFilters,
      status: statusParam, // 使用转换后的状态参数
    };

    const response = await apiClient.post<SubOrderPageResponseOptimized>({
      url: "/api/sub-orders/search",
      data: request
    });

    // 转换为表格组件期望的格式
    return {
      content: response.content,
      //@ts-ignore
      total: response.page.totalElements
    };
  },

  /**
   * 根据ID获取子订单详情
   * @param id 子订单ID
   * @returns 子订单详情
   */
  getSubOrderById: async (id: string): Promise<SubOrder> => {
    return apiClient.get<SubOrder>({
      url: `/api/sub-orders/${id}`
    });
  },

  /**
   * 获取子订单的面单信息
   * @param id 子订单ID
   * @returns 面单信息响应
   */
  getWaybillInfo: async (id: number): Promise<WaybillResponse | null> => {
    try {
      return await apiClient.get<WaybillResponse>({
        url: `/api/sub-orders/${id}/waybill`
      });
    } catch (error) {
      // 如果返回404或其他错误，返回null表示没有面单信息
      return null;
    }
  }
};
