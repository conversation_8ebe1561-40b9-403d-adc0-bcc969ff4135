{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "loading": "Loading...", "confirm": "Confirm", "close": "Close", "search": "Search", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "refresh": "Refresh", "export": "Export", "import": "Import", "add": "Add", "remove": "Remove", "view": "View", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "select": "Select", "selectAll": "Select All", "clear": "Clear", "filter": {"noResults": "No results found.", "selectedCount": "{{count}} selected", "clear": "Clear", "clearFilters": "Clear filters"}, "sort": "Sort", "settings": "Settings", "help": "Help", "about": "About", "version": "Version", "language": "Language", "theme": "Theme", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "rememberMe": "Remember Me", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "ok": "OK", "apply": "Apply", "create": "Create", "update": "Update", "manage": "Manage", "dashboard": "Dashboard", "home": "Home", "menu": "<PERSON><PERSON>", "navigation": "Navigation", "page": "Page", "total": "Total", "items": "Items", "records": "Records", "results": "Results", "noData": "No Data", "noResults": "No Results", "empty": "Empty", "all": "All", "none": "None", "other": "Other", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "enable": "Enable", "disable": "Disable", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "available": "Available", "unavailable": "Unavailable", "public": "Public", "private": "Private", "draft": "Draft", "published": "Published", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "failed": "Failed", "processing": "Processing", "waiting": "Waiting", "timeout": "Timeout", "expired": "Expired", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "required": "Required", "optional": "Optional", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "auto": "Auto", "manual": "Manual"}, "auth": {"loginTitle": "POD System Login", "loginSubtitle": "Welcome to Cross-border Order Management System", "account": "Account", "password": "Password", "accountPlaceholder": "Please enter your account", "passwordPlaceholder": "Please enter your password", "loginButton": "<PERSON><PERSON>", "loginSuccess": "Login successful!", "loginFailed": "<PERSON><PERSON> failed, please try again", "logoutSuccess": "Logout successful", "sessionExpired": "Session expired, please login again", "invalidCredentials": "Invalid account or password", "accountRequired": "Account is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "systemName": "POD", "systemFullName": "Cross-border Order Management System", "systemDescription": "Professional cross-border order management system for smarter and more efficient logistics business", "intelligentOrderManagement": "Intelligent Order Management"}, "navigation": {"platform": "Platform", "dashboard": "Dashboard", "secret": "Secret", "keyManagement": "Key Management", "model": "Model", "organization": "Organization", "user": "User", "wallet": "Wallet", "createOrganization": "Create Organization", "subOrder": "Sub Order"}, "breadcrumb": {"home": "Home", "dashboard": "Dashboard", "organization": "Organization Management", "model": "Model Management", "secret": "Secret Management", "subscribe": "Subscription Management", "wallet": "Wallet Management", "team": "Team Management"}, "dashboard": {"home": "Home", "homeDescription": "View your order overview and trend analysis", "amountUsage": "Amount Usage", "tokenUsage": "Token Usage", "requestCount": "Request Count", "errorRate": "Error Rate", "responseTime": "Response Time", "activeUsers": "Active Users", "totalRevenue": "Total Revenue", "monthlyGrowth": "Monthly Growth", "dailyStats": "Daily Statistics", "weeklyStats": "Weekly Statistics", "monthlyStats": "Monthly Statistics", "yearlyStats": "Yearly Statistics", "tabs": {"overview": "Overview Statistics", "trend": "Trend Analysis"}, "charts": {"orderTrend": {"title": "Order Trend", "description": "Total {{totalOrders}} orders, average {{averageOrders}} per day", "peakDay": "Peak: {{count}} orders ({{date}})", "noData": "No trend data available", "noDataHint": "Please select a valid time range", "labels": {"totalOrders": "Total Orders", "completedOrders": "Completed", "processingOrders": "Processing", "cancelledOrders": "Cancelled"}}, "statusDistribution": {"title": "Order Status Distribution", "description": "Status distribution of {{total}} orders in total", "noData": "No status distribution data available", "tooltip": {"count": "Count", "percentage": "Percentage"}, "statuses": {"CREATED": "Created", "COMPLETED": "Completed", "CANCELLED": "Cancelled", "SPLIT": "Split", "SUPPLIER_MATCHED": "Supplier Matched", "FAILED": "Failed", "PROCESSING": "Processing", "TRACKING_NOT_FOUND": "Tracking Not Found", "TRACKING_PRE_ADVICE_RECEIVED": "Pre-Advice Received", "TRACKING_PENDING": "Tracking Pending", "TRACKING_PICKED_UP": "Picked Up", "TRACKING_IN_TRANSIT": "In Transit", "TRACKING_ARRIVED_DESTINATION_COUNTRY": "Arrived Destination Country", "TRACKING_IN_CUSTOMS": "In Customs", "TRACKING_CUSTOMS_CLEARED": "Customs Cleared", "TRACKING_ARRIVED_FOR_PICKUP": "Arrived for Pickup", "TRACKING_OUT_FOR_DELIVERY": "Out for Delivery", "TRACKING_DELIVERY_FAILED": "Delivery Failed", "TRACKING_DELIVERED": "Delivered", "TRACKING_EXCEPTION": "Tracking Exception", "TRACKING_RETURNED": "Returned", "TRACKING_CANCELLED": "Cancelled (Tracking)", "TRACKING_UNKNOWN": "Unknown Status"}}, "overviewStats": {"totalOrders": "Total Orders", "completedOrders": "Completed", "processingOrders": "Processing", "recentOrders": "Recent 7 Days", "descriptions": {"totalOrders": "Total orders in {{days}} days", "completedOrders": "Completion rate {{rate}}%", "processingOrders": "Orders being processed", "recentOrders": "Recent order activity"}}}, "messages": {"fetchDataFailed": "Failed to fetch data, please try again later"}, "dateRangePicker": {"placeholder": "Select time range", "customDatePlaceholder": "Select date range", "dateRangeDisplay": "{{from}} to {{to}}", "quickOptions": {"7d": "Last 7 days", "30d": "Last 30 days", "90d": "Last 90 days", "custom": "Custom range"}}}, "language": {"english": "English", "chinese": "中文", "vietnamese": "Tiếng <PERSON>", "selectLanguage": "Select Language", "languageChanged": "Language changed successfully"}, "messages": {"getUserInfoFailed": "Failed to get user information: {{message}}", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "saveSuccess": "Save successful", "saveFailed": "Save failed", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "updateSuccess": "Update successful", "updateFailed": "Update failed", "createSuccess": "Create successful", "createFailed": "Create failed", "networkError": "Network error, please try again", "serverError": "Server error, please contact administrator", "permissionDenied": "Permission denied", "dataNotFound": "Data not found", "invalidInput": "Invalid input", "requiredField": "This field is required", "invalidFormat": "Invalid format", "confirmDelete": "Are you sure you want to delete this item?", "confirmAction": "Are you sure you want to perform this action?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "processingRequest": "Processing request...", "requestTimeout": "Request timeout, please try again", "connectionLost": "Connection lost, please check your network"}, "subOrder": {"title": "Sub Order Management", "description": "View and manage all sub order information, support filtering by order number, customer order number, time range and other conditions", "search": {"title": "Search Criteria", "orderNo": "Order Number", "orderNoPlaceholder": "Please enter order number", "customerOrderNo": "Customer Order Number", "customerOrderNoPlaceholder": "Please enter customer order number", "status": "Status", "startTime": "Start Time", "endTime": "End Time", "selectStartTime": "Select start time", "selectEndTime": "Select end time", "searchButton": "Search", "resetButton": "Reset"}, "status": {"CREATED": "Created", "COMPLETED": "Completed", "CANCELLED": "Cancelled", "SPLIT": "Split", "SUPPLIER_MATCHED": "Supplier Matched", "FAILED": "Failed", "PROCESSING": "Processing", "TRACKING_NOT_FOUND": "Tracking Not Found", "TRACKING_PRE_ADVICE_RECEIVED": "Pre-Advice Received", "TRACKING_PENDING": "Pending Pickup", "TRACKING_PICKED_UP": "Picked Up", "TRACKING_IN_TRANSIT": "In Transit", "TRACKING_ARRIVED_DESTINATION_COUNTRY": "Arrived Destination Country", "TRACKING_IN_CUSTOMS": "In Customs", "TRACKING_CUSTOMS_CLEARED": "Customs Cleared", "TRACKING_ARRIVED_FOR_PICKUP": "Arrived for Pickup", "TRACKING_OUT_FOR_DELIVERY": "Out for Delivery", "TRACKING_DELIVERY_FAILED": "Delivery Failed", "TRACKING_DELIVERED": "Delivered", "TRACKING_EXCEPTION": "Exception", "TRACKING_RETURNED": "Returned", "TRACKING_CANCELLED": "Cancelled (Tracking)", "TRACKING_UNKNOWN": "Unknown Status"}, "table": {"columns": {"orderNo": "Order Number", "customerOrderNo": "Customer Order Number", "status": "Status", "orderStatus": "Order Status", "product": "Product Information", "recipient": "Recipient Information", "shipping": "Shipping No", "source": "Source", "orderSource": "Order Source", "createdAt": "Created At", "actions": "Actions"}, "product": {"unknownProduct": "Unknown Product", "quantity": "Quantity", "pieces": "pcs", "hoverToViewDetails": "Hover to view details"}, "recipient": {"unknownRecipient": "Unknown Recipient", "incompleteAddress": "Incomplete address information", "hoverToViewDetails": "Hover to view details"}, "actions": {"viewLabel": "Label", "viewTracking": "View Tracking", "viewWaybill": "View Waybill"}}, "productDetails": {"title": "Product Details", "unknownProduct": "Unknown Product", "spu": "SPU:", "size": "Size:", "color": "Color:", "quantity": "Quantity:", "pieces": "pcs"}, "recipientDetails": {"title": "Recipient Details", "subtitle": "Recipient Details", "unknownRecipient": "Unknown Recipient", "username": "Username:", "phone": "Contact Phone", "email": "Email Address", "shippingAddress": "Shipping Address", "postalCode": "Postal Code:"}, "messages": {"copySuccess": "{{label}} copied to clipboard", "copyFailed": "Co<PERSON> failed"}}, "waybill": {"title": "Waybill Information", "orderNo": "Order No", "loading": "Loading waybill information...", "error": "Loading failed", "retry": "Retry", "noWaybillInfo": "No waybill information available", "fetchError": "Failed to fetch waybill information", "basicInfo": "Basic Information", "waybillNo": "Waybill No", "trackingNumber": "Tracking Number", "pdfDocument": "PDF Document", "viewPdf": "View PDF", "downloadPdf": "Download PDF", "noData": "No Data", "noDataDescription": "No waybill information available for this order"}, "pagination": {"rowsSelected": "{{count}} selected", "totalRows": "of {{count}} row(s)", "rowsPerPage": "Rows per page", "page": "Page {{current}}", "totalPages": "of {{total}}", "goToFirstPage": "Go to first page", "previousPage": "Previous", "nextPage": "Next", "goToLastPage": "Go to last page"}, "tracking": {"title": "Tracking Information", "orderNo": "Order No.", "waybillNo": "Waybill No.", "trackingNumber": "Tracking Number", "channel": "Logistics Channel", "currentStatus": "Current Status", "deliveryDays": "Delivery Days", "days": "days", "lastUpdated": "Last Updated", "lastMileProvider": "Last Mile Provider", "providerName": "Provider Name", "telephone": "Telephone", "website": "Website", "visitWebsite": "Visit Website", "trackingEvents": "Tracking Events", "lastMileEvent": "Last Mile Event", "podLinks": "Proof of Delivery", "viewPod": "View POD", "noTrackingInfo": "No tracking information available", "noEvents": "No tracking events", "fetchError": "Failed to fetch tracking information", "latest": "Latest", "status": {"NOT_FOUND": "Not Found", "PRE_ADVICE_RECEIVED": "Pre-Advice Received", "PICKED_UP": "Picked Up", "IN_TRANSIT": "In Transit", "ARRIVED_DESTINATION_COUNTRY": "Arrived Destination Country", "IN_CUSTOMS": "In Customs", "CUSTOMS_CLEARED": "Customs Cleared", "ARRIVED_FOR_PICKUP": "Arrived for Pickup", "OUT_FOR_DELIVERY": "Out for Delivery", "DELIVERY_FAILED": "Delivery Failed", "DELIVERED": "Delivered", "EXCEPTION": "Exception", "RETURNED": "Returned", "CANCELLED": "Cancelled", "UNKNOWN": "Unknown"}}}