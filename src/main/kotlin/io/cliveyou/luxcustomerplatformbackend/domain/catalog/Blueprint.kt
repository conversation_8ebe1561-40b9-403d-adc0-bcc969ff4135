package io.cliveyou.luxcustomerplatformbackend.domain.catalog

import io.cliveyou.luxcustomerplatformbackend.nextId
import io.cliveyou.luxcustomerplatformbackend.config.jpa.AbstractBaseEntity
import jakarta.persistence.*

/**
 * Blueprint 产品蓝图实体
 * 表示可用的产品模板信息
 */
@Entity(name = "blueprints")
class Blueprint : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    var id: Long = nextId()

    @Column(name = "title", nullable = false)
    var title: String = ""

    @Column(name = "description", columnDefinition = "text")
    var description: String = ""

    @Column(name = "brand", nullable = false)
    var brand: String = ""

    @Column(name = "model", nullable = false)
    var model: String = ""

    @ElementCollection
    @CollectionTable(
        name = "blueprint_images",
        joinColumns = [Join<PERSON><PERSON>umn(name = "blueprint_id")]
    )
    @Column(name = "image_url")
    var images: MutableList<String> = mutableListOf()

    @Column(name = "customer_id", nullable = false)
    var customerId: Long = 0

    @Column(name = "is_active", nullable = false)
    var isActive: Boolean = true
}
