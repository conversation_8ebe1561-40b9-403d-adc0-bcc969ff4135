package io.cliveyou.luxcustomerplatformbackend.application

import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContextHolder
import io.cliveyou.luxcustomerplatformbackend.domain.catalog.Blueprint
import io.cliveyou.luxcustomerplatformbackend.facade.catalog.response.BlueprintResponse
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.BlueprintRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Catalog 应用服务
 * 处理产品目录相关的业务逻辑
 */
@Service
@Transactional(readOnly = true)
class CatalogApplicationService(
    private val blueprintRepository: BlueprintRepository,
) {

    /**
     * 获取可用的蓝图列表
     * 根据用户权限返回对应的蓝图数据
     */
    fun getAvailableBlueprints(): List<BlueprintResponse> {
        log.info { "获取可用蓝图列表" }
        
        val currentUser = UserContextHolder.user
        
        // 如果没有用户上下文，返回示例数据
        if (currentUser?.customerId == null) {
            log.info { "用户未登录，返回示例蓝图数据" }
            return getSampleBlueprints()
        }
        
        // 查询用户的蓝图数据
        val blueprints = blueprintRepository.findByCustomerIdAndIsActiveOrderByCreatedAtDesc(
            currentUser.customerId, 
            true
        )
        
        // 如果用户没有蓝图数据，返回示例数据
        if (blueprints.isEmpty()) {
            log.info { "用户 ${currentUser.customerId} 没有蓝图数据，返回示例数据" }
            return getSampleBlueprints()
        }
        
        log.info { "为用户 ${currentUser.customerId} 返回 ${blueprints.size} 个蓝图" }
        return blueprints.map { BlueprintResponse.from(it) }
    }
    
    /**
     * 获取示例蓝图数据
     * 用于演示和测试
     */
    private fun getSampleBlueprints(): List<BlueprintResponse> {
        return listOf(
            BlueprintResponse(
                id = 3,
                title = "Kids Regular Fit Tee",
                description = "Description goes here",
                brand = "Delta",
                model = "11736",
                images = listOf(
                    "https://images.printify.com/5853fe7dce46f30f8327f5cd",
                    "https://images.printify.com/5c487ee2a342bc9b8b2fc4d2"
                )
            ),
            BlueprintResponse(
                id = 5,
                title = "Men's Cotton Crew Tee",
                description = "Description goes here",
                brand = "Next Level",
                model = "3600",
                images = listOf(
                    "https://images.printify.com/5a2ffc81b8e7e3656268fb44",
                    "https://images.printify.com/5cdc0126b97b6a00091b58f7"
                )
            ),
            BlueprintResponse(
                id = 6,
                title = "Unisex Heavy Cotton Tee",
                description = "Description goes here",
                brand = "Gildan",
                model = "5000",
                images = listOf(
                    "https://images.printify.com/5a2fd7d9b8e7e36658795dc0",
                    "https://images.printify.com/5c595436a342bc1670049902",
                    "https://images.printify.com/5c595427a342bc166b6d3002",
                    "https://images.printify.com/5a2fd022b8e7e3666c70623a"
                )
            ),
            BlueprintResponse(
                id = 9,
                title = "Women's Favorite Tee",
                description = "Description goes here",
                brand = "Bella+Canvas",
                model = "6004",
                images = listOf(
                    "https://images.printify.com/5a2ffeeab8e7e364d660836f",
                    "https://images.printify.com/59e362cab8e7e30a5b0a55bd",
                    "https://images.printify.com/59e362d2b8e7e30b9f576691",
                    "https://images.printify.com/59e362ddb8e7e3174f3196ee",
                    "https://images.printify.com/59e362eab8e7e3593e2ac98d"
                )
            ),
            BlueprintResponse(
                id = 10,
                title = "Women's Flowy Racerback Tank",
                description = "Description goes here",
                brand = "Bella+Canvas",
                model = "8800",
                images = listOf(
                    "https://images.printify.com/5a27eb68b8e7e364d6608322",
                    "https://images.printify.com/5c485236a342bc521c2a0beb",
                    "https://images.printify.com/5c485217a342bc686053da46",
                    "https://images.printify.com/5c485225a342bc52fe5fee83"
                )
            ),
            BlueprintResponse(
                id = 11,
                title = "Women's Jersey Short Sleeve Deep V-Neck Tee",
                description = "Description goes here",
                brand = "Bella+Canvas",
                model = "6035",
                images = listOf(
                    "https://images.printify.com/5a27f20fb8e7e316f403a3b1",
                    "https://images.printify.com/5c472ff0a342bcad97372d72",
                    "https://images.printify.com/5c472ff8a342bcad9964d115"
                )
            ),
            BlueprintResponse(
                id = 12,
                title = "Unisex Jersey Short Sleeve Tee",
                description = "Description goes here",
                brand = "Bella+Canvas",
                model = "3001",
                images = listOf(
                    "https://images.printify.com/5a2ff5b0b8e7e36669068406",
                    "https://images.printify.com/59e35414b8e7e30aa625995c",
                    "https://images.printify.com/5cd579548c3769000f274cac",
                    "https://images.printify.com/5cd579558c37690008453286",
                    "https://images.printify.com/59e3541bb8e7e30a60795f9c",
                    "https://images.printify.com/59e35428b8e7e30a1a4de812",
                    "https://images.printify.com/59e3552db8e7e3174714887a",
                    "https://images.printify.com/5a8beec5b8e7e304614eb59c"
                )
            )
        )
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
