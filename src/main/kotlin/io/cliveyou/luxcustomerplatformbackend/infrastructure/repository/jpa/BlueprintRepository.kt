package io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa

import io.cliveyou.luxcustomerplatformbackend.domain.catalog.Blueprint
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

/**
 * Blueprint 数据访问接口
 * 提供 Blueprint 实体的数据库操作
 */
@Repository
interface BlueprintRepository : JpaRepository<Blueprint, Long>, JpaSpecificationExecutor<Blueprint> {
    
    /**
     * 根据客户ID和激活状态查询蓝图列表
     */
    fun findByCustomerIdAndIsActiveOrderByCreatedAtDesc(customerId: Long, isActive: Boolean): List<Blueprint>
    
    /**
     * 根据激活状态查询所有蓝图列表
     */
    fun findByIsActiveOrderByCreatedAtDesc(isActive: Boolean): List<Blueprint>
}
