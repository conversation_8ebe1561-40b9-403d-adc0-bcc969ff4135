package io.cliveyou.luxcustomerplatformbackend.facade.catalog

import io.cliveyou.luxcustomerplatformbackend.application.CatalogApplicationService
import io.cliveyou.luxcustomerplatformbackend.facade.catalog.response.BlueprintResponse
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Catalog 控制器
 * 提供产品目录相关的 OpenAPI 接口
 */
@RestController
@RequestMapping("/v1/catalog")
class CatalogController(
    private val catalogApplicationService: CatalogApplicationService,
) {

    /**
     * 获取可用的蓝图列表
     * 
     * GET /v1/catalog/blueprints.json
     * 
     * 这是一个 OpenAPI 接口，供客户使用
     * 返回当前可用的产品蓝图列表
     * 
     * @return 蓝图列表，直接返回数组格式（不包装在标准响应结构中）
     */
    @GetMapping("/blueprints")
    fun getBlueprints(): List<BlueprintResponse> {
        log.info { "收到获取蓝图列表请求" }
        return catalogApplicationService.getAvailableBlueprints()
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
