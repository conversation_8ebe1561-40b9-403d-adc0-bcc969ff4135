package io.cliveyou.luxcustomerplatformbackend.facade.catalog.response

import io.cliveyou.luxcustomerplatformbackend.domain.catalog.Blueprint

/**
 * Blueprint 响应DTO
 * 用于返回产品蓝图信息给客户端
 */
data class BlueprintResponse(
    val id: Long,
    val title: String,
    val description: String,
    val brand: String,
    val model: String,
    val images: List<String>,
) {
    companion object {
        fun from(blueprint: Blueprint): BlueprintResponse {
            return BlueprintResponse(
                id = blueprint.id,
                title = blueprint.title,
                description = blueprint.description,
                brand = blueprint.brand,
                model = blueprint.model,
                images = blueprint.images,
            )
        }
    }
}
